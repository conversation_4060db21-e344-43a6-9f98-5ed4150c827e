<template>
  <view class="scanCode-page">
    <nav-bar back>扫码入库</nav-bar>
      <!-- 操作区域 -->
      <view class="action-section">
        <view class="section-title">扫码入库操作</view>
        <view class="action-buttons">
          <button class="action-btn primary" @click="showBoardTypeDialog">
            选择板型并扫码
          </button>
        </view>
      </view>
    </view>
</template>

<script>
import { inboundSemiFinishedProduct, showMessage } from '@/api/workOrders.js'

export default {
  name: "scanCode",
  data() {
    return {
      // 页面参数
      orderCode: '',
      stepTaskId: '',
      quantity: '',
      boardType: '',
      productName: ''
    }
  },

  onLoad(options) {
    // 接收页面参数
    this.orderCode = decodeURIComponent(options.orderCode || '')
    this.stepTaskId = options.stepTaskId || ''
    this.quantity = options.quantity || ''
    this.boardType =decodeURIComponent(options.boardType || '')
    this.productName = decodeURIComponent(options.productName || '')

    console.log('scanCode页面接收参数:', {
      orderCode: this.orderCode,
      stepTaskId: this.stepTaskId,
      quantity: this.quantity,
      boardType: this.boardType,
      productName: this.productName
    })

    // 参数验证
    if (!this.orderCode || !this.stepTaskId || !this.quantity || !this.boardType || !this.productName) {
      showMessage('页面参数不完整', 'error')
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
      return
    }

    // 页面加载完成后，延迟一下直接显示板型选择对话框
    this.$nextTick(() => {
      setTimeout(() => {
        this.showBoardTypeDialog()
      }, 500) // 延迟500ms确保页面完全加载
    })
  },

  methods: {
    /** 显示板型选择对话框 */
    showBoardTypeDialog() {
      console.log('显示板型选择对话框，产品板型:', this.boardType)

      // 根据产品板型构建选项列表
      let itemList = []
      let boardTypeMapping = []

      if (this.boardType === '上下板') {
        // 上下板产品：显示上板入库和下板入库选项
        itemList.push('上板入库')
        boardTypeMapping.push('上板')

        itemList.push('下板入库')
        boardTypeMapping.push('下板')
      } else if (this.boardType === '单板') {
        // 单板产品：只显示单板入库选项
        itemList.push('单板入库')
        boardTypeMapping.push('单板')
      } else {
        // 其他情况：显示所有选项
        itemList.push('上板入库')
        boardTypeMapping.push('上板')

        itemList.push('下板入库')
        boardTypeMapping.push('下板')

        itemList.push('单板入库')
        boardTypeMapping.push('单板')
      }

      console.log('显示板型选择对话框:', {
        boardType: this.boardType,
        itemList: itemList,
        boardTypeMapping: boardTypeMapping
      })

      // 显示板型选择对话框
      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const selectedBoardType = boardTypeMapping[res.tapIndex]
          console.log('用户选择的板型:', selectedBoardType)

          // 选择完成后进入扫码流程
          this.startScanCode(selectedBoardType)
        },
        fail: (err) => {
          console.log('用户取消选择板型')
        }
      })
    },

    /** 开始扫码流程 */
    startScanCode(boardType) {
      console.log('开始扫码，板型:', boardType)

      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          console.log('扫码成功，结果:', res.result)
          // 扫码成功，处理扫码结果
          this.processScanResult(res.result, boardType)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          // 扫码失败处理
          this.handleScanError(err)
        }
      })
    },

    /** 处理扫码结果 */
    processScanResult(scanData, boardType) {
      try {
        console.log('处理扫码结果:', {
          scanData: scanData,
          boardType: boardType,
          productName: this.productName,
          quantity: this.quantity
        })

        // 解析扫码数据，提取purchase_no
        const purchaseNo = this.extractPurchaseNo(scanData)
        console.log('解析出的purchaseNo:', purchaseNo)

        if (!purchaseNo) {
          uni.showToast({
            title: `扫码数据格式错误，无法解析采购单号。\n扫码内容：${scanData}`,
            icon: 'none',
            duration: 5000
          })
          return
        }

        // 构建确认内容
        const content = `确定要入库 ${this.productName} 的${boardType}吗？\n数量：${this.quantity}\n采购单号：${purchaseNo}`

        // 显示扫码结果确认弹窗
        uni.showModal({
          title: '扫码结果确认',
          content: content,
          confirmText: '确定',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户确认，执行入库操作
              this.performInbound(purchaseNo, boardType)
            }
          }
        })
      } catch (error) {
        console.error('处理扫码结果失败:', error)
        uni.showToast({
          title: '处理扫码结果失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        })
      }
    },

    /** 从扫码数据中提取purchase_no */
    extractPurchaseNo(scanData) {
      try {
        // 首先尝试解析JSON格式：{"label_type": "purchase", "purchase_no": "PO20250729142"}
        try {
          const data = JSON.parse(scanData)
          if (data.label_type === 'purchase' && data.purchase_no) {
            console.log('JSON格式解析出的purchase_no:', data.purchase_no)
            return data.purchase_no
          }
        } catch (jsonError) {
          // JSON解析失败，继续尝试其他格式
        }

        // 扫码数据格式示例：purchase_no:PO20250729142 或 label_type:purchase|purchase_no:PO20250729142
        if (!scanData || typeof scanData !== 'string') {
          return null
        }

        // 使用正则表达式提取purchase_no的值
        const purchaseNoMatch = scanData.match(/purchase_no:([^|]+)/i)

        if (purchaseNoMatch && purchaseNoMatch[1]) {
          const purchaseNo = purchaseNoMatch[1].trim()
          console.log('正则匹配解析出的purchase_no:', purchaseNo)
          return purchaseNo
        }

        // 如果正则匹配失败，尝试手动解析
        const parts = scanData.split('|')
        for (const part of parts) {
          if (part.includes('purchase_no:')) {
            const purchaseNo = part.split('purchase_no:')[1]?.trim()
            if (purchaseNo) {
              console.log('手动解析出的purchase_no:', purchaseNo)
              return purchaseNo
            }
          }
        }

        // 如果都失败了，检查是否整个字符串就是采购单号
        if (scanData.startsWith('PO') && scanData.length > 5) {
          console.log('直接使用扫码数据作为purchase_no:', scanData)
          return scanData
        }

        return null
      } catch (error) {
        console.error('解析purchase_no失败:', error)
        return null
      }
    },
    /** 执行入库操作 */
    async performInbound(purchaseNo, boardType) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '入库中...'
        })

        // 准备API参数
        const formData = {
          quantity: this.quantity,           // 从页面参数获取的数量
          purchaseNo: purchaseNo,           // 从扫码结果解析的采购单号
          itemName: this.productName,       // 从页面参数获取的产品名称
          boardType: boardType,             // 用户选择的板型（"上板"/"下板"/"单板"）
          stepTaskId: this.stepTaskId       // 从页面参数获取的工序任务ID
        }

        console.log('入库API调用参数:', formData)

        // 调用入库API
        const response = await inboundSemiFinishedProduct(formData)

        console.log('入库API响应:', response)
        console.log('响应码:', response.code)
        console.log('响应消息:', response.message)

        uni.hideLoading()

        // 根据WMS API规范，成功响应的code应该是0
        if (response && (response.code === 0 || response.code === 200)) {
          // 显示成功提示
          uni.showToast({
            title: '入库成功',
            icon: 'success',
            duration: 2000
          })

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)

        } else {
          console.error('入库失败详情:', {
            code: response?.code,
            message: response?.message,
            data: response?.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response?.message || response?.msg || '入库失败，请稍后重试'
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 5000
          })
        }

      } catch (error) {
        uni.hideLoading()

        console.error('入库操作异常:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        })

        // 处理不同类型的错误
        let errorMessage = '网络请求失败，请检查网络连接'

        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试'
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络'
          } else {
            errorMessage = error.message
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 5000
        })
      }
    },

    /** 处理扫码失败 */
    handleScanError(error) {
      console.error('扫码失败:', error)

      let errorMessage = '扫码失败'

      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMessage = '用户取消扫码'
        } else if (error.errMsg.includes('fail')) {
          errorMessage = '扫码功能异常，请重试'
        }
      }

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
    }
  }
}
</script>

<style scoped lang="scss">
.scanCode-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  padding: 20rpx;
}

/* 信息显示区域 */
.info-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 操作区域 */
.action-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    }
  }
}
</style>